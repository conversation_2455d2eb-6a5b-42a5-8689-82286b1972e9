import '@/icons/index'
import ApaasCustomMyWebmobile from './custom-page/page.vue'
import { customFormComponentList } from './custom-component/form-component'
import { widgetConfigList } from './custom-component/form-config'
import ApaasCustomMarketingLeads from './custom-page/marketing-leads/Index.vue'
const install = function(Vue, opts) {
  window.Vue = Vue
  setLink('https://cdnjs.cloudflare.com/ajax/libs/vant/2.12.48/index.min.css')
  setScript('https://cdnjs.cloudflare.com/ajax/libs/vant/2.12.48/vant.js', Vue)
  // 安装MyWeb-mobile模块, 此处的和apaas.json定义的路由，必须一致
  Vue.component('apaas-custom-myWeb-mobile', ApaasCustomMyWebmobile)
  // apaas-custom-marketing-leads
  Vue.component('apaas-custom-marketing-leads', ApaasCustomMarketingLeads)
  // 安装表单部件
  // 注册自开发表单组件
  if (customFormComponentList && Array.isArray(customFormComponentList)) {
    customFormComponentList.forEach((comp) => {
      Vue.component(comp.name, comp)
    })
  }
  // 表单引擎注册自开发组件配置
  if (
    (!opts || !opts.onlyInstallFormWidget) &&
    widgetConfigList &&
    Array.isArray(widgetConfigList)
  ) {
    widgetConfigList.forEach((widgetConfig) => {
      const compConfig = {
        widgetConfig
      }
      Vue.FormEngine && Vue.FormEngine.registerCustomComponentConfig(compConfig)
    })
  }
}

function setScript(url, Vue) {
  const htmlElement = document.documentElement
  const fragment = document.createDocumentFragment()
  const script = document.createElement('script')
  script.setAttribute('type', 'text/javascript')
  script.setAttribute('src', url)
  script.setAttribute('crossorigin', 'anonymous')
  script.setAttribute('referrerpolicy', 'no-referrer')
  //   console.log('script:', script)
  script.onload = function() {
    // console.log(1222, window.vant)
    Vue.use(window.vant)
  }
  fragment.append(script)
  htmlElement.append(fragment)
}

function setLink(url) {
  var link = document.createElement('link')
  link.setAttribute('rel', 'stylesheet')
  link.setAttribute('type', 'text/css')
  link.setAttribute('href', url)
  //   console.log('link:', link)
  document.getElementsByTagName('head')[0].appendChild(link)
}

const MyWebMobileCustomPlugin = {
  install: install
}

export default MyWebMobileCustomPlugin
