<template>
  <div class="app">
    <div class="banner"></div>
    <!-- Header -->
    <div class="header">
      <van-icon name="arrow-left" size="30" color="#FFFFFF" @click="goBack" />
      <h1 class="title">
        线索列表
      </h1>
      <div style="width: 30px;height: 30px;"></div>
    </div>
    <!-- Date Range Selector with Tabs -->
    <DateSelector
      :date-range="dateRange"
      :current-filter="currentFilter"
      :active-tab="activeTab"
      @date-update="handleDateUpdate"
      @show-filter-options="showFilterOptions"
      @apply-filters="applyFilters"
      @perform-search="performSearch"
      @switch-tab="switchTab"
      @update:dateRange="handleDateRangeUpdate"
    />

    <!-- Content Area -->
    <div class="content">
      <div>
        <keep-alive>
          <component
            :is="currentComponent"
            :key="componentKey"
            :date-range="dateRange"
            :filters="currentFilter"
            @loading="handleLoading"
            @error="handleError"
            @data-updated="handleDataUpdated"
            @clickTableRow="clickTableRow"
            @clickTableCell="clickTableCell"
          />
        </keep-alive>
      </div>
    </div>
  </div>
</template>

<script>
import LeadProgress from './components/LeadProgress.vue'
import DepartmentChart from './components/DepartmentChart.vue'
import DailyReport from './components/DailyReport.vue'
import DateSelector from './components/DateSelector.vue'
import dayjs from 'dayjs'

export default {
  name: 'ApaasCustomMarketingLeads',
  components: {
    LeadProgress,
    DepartmentChart,
    DailyReport,
    DateSelector
  },
  data() {
    return {
      activeTab: 'progress',
      loading: false,
      error: null,
      dateRange: {
        startDate: dayjs()
          .startOf('year')
          .format('YYYY/MM/DD'),
        endDate: dayjs().format('YYYY/MM/DD'),
        countType: '线索条数'
      },
      currentFilter: {
        value: 'all',
        label: '线索类型'
      },
      componentKey: 0
    }
  },
  computed: {
    currentComponent() {
      const componentMap = {
        progress: 'LeadProgress',
        department: 'DepartmentChart',
        daily: 'DailyReport'
      }
      return componentMap[this.activeTab]
    }
  },
  async mounted() {
    // 获取第一个匹配的元素
    var element = document.querySelector('.nav-bar-content')

    // 如果元素存在，则隐藏
    if (element) {
      element.style.display = 'none'
    }
  },
  beforeDestroy() {
    // 在组件销毁前恢复 .nav-bar-content 的显示
    const element = document.querySelector('.nav-bar-content')
    if (element) {
      element.style.display = ''
    }
  },
  methods: {
    switchTab(tab) {
      if (this.activeTab !== tab) {
        this.dateRange.ids = []
        // console.log('当前选中的线索类型：', this.dateRange)
        this.activeTab = tab
        this.error = null
        // 强制重新渲染组件以触发数据刷新
        this.componentKey++
      }
    },

    showDatePicker(type) {
      // 显示日期选择器的逻辑
      console.log(`Show date picker for ${type}`)
    },

    showFilterOptions() {
      // 显示筛选选项的逻辑
      console.log('Show filter options')
    },

    applyFilters(e) {
      // console.log('Apply filters', )
      this.dateRange = e
      // 应用筛选条件
      this.componentKey++
      console.log('Apply filters')
    },

    performSearch(e) {
      // console.log('Perform search', e)
      this.dateRange.ids = []
      // 执行搜索
      this.componentKey++
      // console.log('执行搜索，Perform search')
    },

    handleLoading(isLoading) {
      this.loading = isLoading
    },

    handleError(errorMessage) {
      this.error = errorMessage
      this.loading = false
    },
    handleDateUpdate(data) {
      console.log('Handle date update', data)

      this.dateRange = { ...this.dateRange, ...data }
    },

    handleDataUpdated(data) {
      console.log('Data updated:', data)
      this.loading = false
      this.error = null
    },
    clickTableRow(data) {
      console.log('点击表格行', data)
      // activeTab: 'progress',
      this.dateRange.ids = data.ids
      this.activeTab = 'progress'
      this.error = null
      // 强制重新渲染组件以触发数据刷新
      this.componentKey++
    },
    clickTableCell(data) {
      console.log('点击表格行', data)
      // activeTab: 'progress',
      this.dateRange.ids = data.columnIds
      this.activeTab = 'progress'
      this.error = null
      // 强制重新渲染组件以触发数据刷新
      this.componentKey++
    },
    retryLoad() {
      this.error = null
      this.componentKey++
    },
    handleDateRangeUpdate(newDateRange) {
      console.log('Handle date range update:', newDateRange)
      this.dateRange = { ...this.dateRange, ...newDateRange }
      // 只要countType变化就刷新当前tab
      if (newDateRange.countType) {
        console.log(`Refreshing ${this.activeTab} tab`)
        this.componentKey++
      }
    },
    goBack() {
      this.$router.go(-1)
    }
  }
}
</script>

<style scoped>
.app {
  position: fixed;
  z-index: 1;
  width: 100vw;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  /* background: #f5f5f5; */
  min-height: 100vh;
  margin: 0 auto;
  background-color: #e6f0fa;
  background-image: linear-gradient(
    180deg,
    #0958d9 0%,
    #1677ff 8%,
    #2690f2 12%,
    rgba(0, 167, 255, 0.3) 15%,
    rgba(208, 228, 250, 0) 21%
  );
}
/* .banner {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  z-index: -1;
  background: url('./assets/img/bg-banner.png') 100% 0% no-repeat;
  background-size: 100% 200px;
  width: 100%;
  height: 200px;
} */

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  /* text-align: center; */
  padding: 12px 0;
  background: transparent;
}

.title {
  flex: 1;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-align: center;
}

.content {
  border-radius: 16px 16px 0 0;
  min-height: calc(100vh - 200px);
  padding: 16px;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #666;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.error-message {
  color: #ff4d4f;
  margin-bottom: 16px;
  text-align: center;
}

.retry-btn {
  background: #4a90e2;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
}
</style>
