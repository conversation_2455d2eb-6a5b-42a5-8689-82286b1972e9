<template>
  <div>
    <!-- 弹出层 -->
    <van-popup
      v-model="show"
      position="bottom"
      :style="{ height: '80%' }"
      round
      closeable
      close-icon-position="top-right"
      @close="handlerClose"
    >
      <div class="filter-popup">
        <div class="popup-header">
          <h3 class="popup-title">
            筛选条件
          </h3>
        </div>
        <div class="popup-content">
          <van-form @submit="onSubmit">
            <!-- 日期区间选择 -->
            <van-cell title="创建时间" :value="date" is-link @click="showCalendar = true" />
            <van-calendar
              v-model="showCalendar"
              color="#1677FF"
              type="range"
              :min-date="new Date(2010, 0, 1)"
              :max-date="new Date()"
              @confirm="onDateConfirm"
            />
            <van-field
              v-model="formData.clueType"
              label="意向动力"
              placeholder="请选择意向动力"
              readonly
              is-link
              @click="showClueTypePicker = true"
            />
            <!-- 城市 -->
            <van-field v-model="formData.region" label="市场部" placeholder="请输入市场部" />
            <!-- 城市 -->
            <van-field v-model="formData.city" label="城市" placeholder="请输入城市" />
            <!-- 线索来源 -->
            <yc-picker
              :selectedValue="formData.clueFrom"
              :options="getDictByType('CRM_XSLY')"
              label="线索来源"
              @change="onClueFromConfirm"
            />
            <yc-picker
              :selectedValue="formData.brand"
              :options="getDictByType('strain')"
              label="意向整车品牌"
              @change="onBrandConfirm"
            />
            <!-- 燃料类型 -->
            <van-field v-model="formData.fuel" label="燃料类型" placeholder="请输入燃料类型" />
            <!-- 需求数量 -->
            <van-field
              v-model="formData.needNum"
              label="需求数量"
              placeholder="请输入需求数量"
              type="number"
            />
            <van-cell
              title="采购日期"
              :value="purchaseDate"
              is-link
              @click="showPurchaseDate1Picker = true"
            />
            <van-calendar
              v-model="showPurchaseDate1Picker"
              color="#1677FF"
              type="range"
              :min-date="new Date(2010, 0, 1)"
              :max-date="new Date()"
              @confirm="onPurchaseDateConfirm"
            />
          </van-form>
        </div>

        <div class="popup-footer">
          <van-button class="reset-btn" plain type="default" @click="onReset">
            重置
          </van-button>
          <van-button class="confirm-btn" type="primary" @click="onConfirm">
            确认
          </van-button>
        </div>
      </div>
    </van-popup>

    <!-- 线索类型选择器 -->
    <van-popup v-model="showClueTypePicker" position="bottom">
      <van-picker
        :columns="clueTypeOptions"
        title="选择意向动力"
        :show-toolbar="true"
        @confirm="onClueTypeConfirm"
        @cancel="showClueTypePicker = false"
      />
    </van-popup>

    <!-- 区域选择器 -->
    <van-popup v-model="showRegionPicker" position="bottom">
      <van-picker
        :columns="regionOptions"
        title="选择区域"
        :show-toolbar="true"
        @confirm="onRegionConfirm"
        @cancel="showRegionPicker = false"
      />
    </van-popup>
    <!-- 城市选择器 -->
    <van-popup v-model="showCityPicker" position="bottom">
      <van-picker
        :columns="cityOptions"
        title="选择城市"
        :show-toolbar="true"
        @confirm="onCityConfirm"
        @cancel="showCityPicker = false"
      />
    </van-popup>
  </div>
</template>

<script>
import { Popup, Form, Field, Button, DatetimePicker, Picker, Calendar } from 'vant'
import YcPicker from '@/components/ycui/YcPicker'
import dictMixin from '../../mixin/dict.mixin'

export default {
  name: 'FilterPopup',
  components: {
    [Popup.name]: Popup,
    [Form.name]: Form,
    [Field.name]: Field,
    [Button.name]: Button,
    [DatetimePicker.name]: DatetimePicker,
    [Picker.name]: Picker,
    [Calendar.name]: Calendar,
    [YcPicker.name]: YcPicker
  },
  mixins: [dictMixin],
  props: {
    value: {
      type: Boolean,
      default: false
    },
    filterData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      show: this.value,
      showCalendar: false,
      date: '',
      purchaseDate: '',
      formData: {
        startDate: '',
        endDate: '',
        clueType: '',
        region: '',
        city: '',
        clueFrom: '',
        brand: '',
        fuel: '',
        needNum: '',
        purchaseDate1: '',
        purchaseDate2: ''
      },
      // 日期选择器相关
      showPurchaseDate1Picker: false,
      showPurchaseDate2Picker: false,
      purchaseDate1Value: new Date(),
      purchaseDate2Value: new Date(),
      // 选择器相关
      showClueTypePicker: false,
      showRegionPicker: false,
      showCityPicker: false,
      showClueFromPicker: false,
      showBrandPicker: false,
      showFuelPicker: false,
      // 选项数据 - 修改为字符串数组格式
      clueTypeOptions: ['玉柴', '竞品'],
      regionOptions: [
        '华北地区',
        '华东地区',
        '华南地区',
        '华中地区',
        '西北地区',
        '西南地区',
        '东北地区'
      ],
      cityOptions: ['北京', '上海', '广州', '深圳', '杭州', '武汉', '成都'],
      clueFromOptions: [
        { value: 'tyc', label: '天眼查' },
        { value: 'qcc', label: '企查查' },
        { value: 'baidu', label: '百度推广' },
        { value: 'website', label: '官网咨询' },
        { value: 'phone', label: '电话咨询' },
        { value: 'exhibition', label: '展会' }
      ],
      brandOptions: ['品牌A', '品牌B', '品牌C', '品牌D'],
      fuelOptions: ['汽油', '柴油', '电动', '混合动力']
    }
  },
  watch: {
    value(val) {
      this.show = val
    },
    filterData: {
      handler(val) {
        this.formData = { ...val }
        if (val.startDate && val.endDate) {
          this.date = `${val.startDate} - ${val.endDate}`
        } else {
          this.date = ''
        }
      },
      immediate: true,
      deep: true
    }
  },
  methods: {
    // 日期确认方法
    onStartDateConfirm(value) {
      this.formData.startDate = this.formatDate(value)
      this.showStartDatePicker = false
    },
    onEndDateConfirm(value) {
      this.formData.endDate = this.formatDate(value)
      this.showEndDatePicker = false
    },
    onPurchaseDateConfirm(date) {
      const [start, end] = date
      this.formData.purchaseDate1 = this.formatDate(start, true)
      this.formData.purchaseDate2 = this.formatDate(end, true)
      this.showPurchaseDate1Picker = false
      this.purchaseDate = `${this.formatDate(start, true)} - ${this.formatDate(end, true)}`
    },
    onDateRangeConfirm(date) {
      const [start, end] = date
      this.dateRangeText = `${this.formatDate(start)} - ${this.formatDate(end)}`
      this.formData.startDate = this.formatDate(start)
      this.formData.endDate = this.formatDate(end)
      this.showDateRangePicker = false
    },
    // 选择器确认方法 - 修改为接收字符串值
    onClueTypeConfirm(value, index) {
      this.formData.clueType = value
      this.showClueTypePicker = false
    },
    onRegionConfirm(value, index) {
      this.formData.region = value
      this.showRegionPicker = false
    },
    onCityConfirm(value, index) {
      this.formData.city = value
      this.showCityPicker = false
    },
    onClueFromConfirm(value) {
      console.log('测试参数1', value)
      this.formData.clueFrom = value
      this.showClueFromPicker = false
    },
    onBrandConfirm(value, index) {
      console.log('测试参数2', value)
      this.formData.brand = value
      this.showBrandPicker = false
    },

    // 工具方法
    formatDate(date, includeTime = false) {
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      if (includeTime) {
        const hours = String(date.getHours()).padStart(2, '0')
        const minutes = String(date.getMinutes()).padStart(2, '0')
        const seconds = String(date.getSeconds()).padStart(2, '0')
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
      }
      return `${year}/${month}/${day}`
    },
    onDateConfirm(date) {
      const [start, end] = date
      this.showCalendar = false
      this.date = `${this.formatDate(start)} - ${this.formatDate(end)}`
      this.formData.startDate = this.formatDate(start)
      this.formData.endDate = this.formatDate(end)
    },

    // 表单操作
    onSubmit() {
      this.onConfirm()
    },
    onConfirm() {
      this.$emit('confirm', { ...this.formData })
    },
    onReset() {
      const today = new Date()
      const firstDayOfYear = new Date(today.getFullYear(), 0, 1)

      this.formData = {
        startDate: this.formatDate(firstDayOfYear),
        endDate: this.formatDate(today),
        clueType: '',
        countType: '线索条数',
        region: '',
        city: '',
        clueFrom: '',
        brand: '',
        fuel: '',
        needNum: '',
        purchaseDate1: '',
        purchaseDate2: '',
        series: ''
      }
      this.purchaseDate = ''
      this.date = `${this.formatDate(firstDayOfYear)} - ${this.formatDate(today)}`
      this.$emit('reset', this.formData)
    },
    handlerClose() {
      this.$emit('close')
    }
  }
}
</script>

<style scoped>
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  padding: 16px;
  border-bottom: 1px solid #ebedf0;
  text-align: center;
}

.popup-title {
  font-size: 16px;
  font-weight: 500;
  color: #323233;
  margin: 0;
}

.popup-content {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.popup-footer {
  padding: 16px;
  border-top: 1px solid #ebedf0;
  display: flex;
  gap: 12px;
}

.reset-btn {
  flex: 1;
}

.confirm-btn {
  flex: 2;
}

/* 覆盖vant样式 */
.van-field {
  padding: 12px 16px;
}

.van-field__label {
  width: 80px;
  color: #646566;
}

.van-field__control {
  color: #323233;
}

.van-field__control::placeholder {
  color: #c8c9cc;
}

/* 确保选择器工具栏显示 */
.van-picker__toolbar {
  display: flex !important;
}

.van-datetime-picker__toolbar {
  display: flex !important;
}
</style>
