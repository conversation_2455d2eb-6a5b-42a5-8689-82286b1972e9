<template>
  <div>
    <div style="padding: 0 15px;">
      <div class="date-selector">
        <div
          style="display: flex;flex:1;width: 100%;align-items: center;"
          @click="showCalendar = true"
        >
          <div class="date-input">
            <span>{{ dateRange?.startDate || '请选择开始日期' }}</span>
            <span class="arrow">▼</span>
          </div>
          <div class="to">
            至
          </div>
          <div class="date-input">
            <span>{{ dateRange?.endDate || '请选择结束日期' }}</span>
            <span class="arrow">▼</span>
          </div>
        </div>
        <div class="filter-bar">
          <div class="filter-item" @click="showCountTypePicker = true">
            <span>{{ form?.series || '请选择品系' }}</span>
            <span class="arrow">▼</span>
          </div>
          <div class="filter-actions">
            <div class="filter-btn" @click="showFilterPopup = true">
              <van-icon name="filter-o" />
              筛选
            </div>
            <div class="search-btn" @click="$emit('perform-search')">
              <van-icon name="search" />
              查询
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 新增的线索类型选择器 -->
    <van-popup v-model="showCountTypePicker" position="bottom">
      <van-picker
        show-toolbar
        :columns="getDictByType('new_clue_strain').map((x) => x.valueName)"
        @confirm="onSeriesConfirm"
        @cancel="showCountTypePicker = false"
      />
    </van-popup>

    <div class="tab-nav">
      <div
        v-for="(tab, index) in tabs"
        :key="index"
        :class="['tab-item', { active: activeTab === tab.value }]"
        @click="$emit('switch-tab', tab.value)"
      >
        {{ tab.label }}
      </div>
    </div>
    <!-- {{ activeTab }} -->
    <div v-if="activeTab != 'progress'" style="padding: 20px 4px 0px 4px;">
      <van-tabs type="card" background="transparent" color="#1b7aff" @click="handleTabClick">
        <van-tab v-for="(item, index) in countTypeOptions" :key="index" :title="item"></van-tab>
      </van-tabs>
    </div>
    <!-- 日历 -->
    <van-calendar
      v-model="showCalendar"
      color="#1677FF"
      type="range"
      :min-date="new Date(2010, 0, 1)"
      :max-date="new Date()"
      @confirm="onDateConfirm"
    />
    <!-- 新增的表单弹出层 -->
    <filter-popup
      v-model="showFilterPopup"
      :filterData="dateRange"
      @confirm="handleFilterSubmit"
      @reset="handleFilterReset"
      @close="handleColse"
    />
  </div>
</template>

<script>
// 导入新的弹出层组件
// import DateFilterPopup from '@/components/date-filter-popup.vue'
import dayjs from 'dayjs'
import FilterPopup from './FilterPopup.vue'
import { Calendar, Popup, Picker } from 'vant'
import dictMixin from '../../mixin/dict.mixin'
import YcPicker from '@/components/ycui/YcPicker'

export default {
  name: 'DateSelector',
  components: {
    FilterPopup,
    [Calendar.name]: Calendar,
    [Popup.name]: Popup,
    [Picker.name]: Picker,
    [YcPicker.name]: YcPicker
  },
  mixins: [dictMixin],
  props: {
    dateRange: {
      type: Object,
      required: true,
      default: () => ({
        start: '开始日期',
        end: '结束日期',
        countType: null, // 新增字段
        series: null
      })
    },
    currentFilter: {
      type: Object,
      required: true,
      default: () => ({
        value: 'all',
        label: '线索类型'
      })
    },
    activeTab: {
      type: String,
      default: 'progress'
    }
  },
  data() {
    return {
      showCalendar: false,
      tabs: [
        { label: '线索进度', value: 'progress' },
        { label: '按市场部', value: 'department' },
        { label: '线索日报', value: 'daily' }
      ],
      showFilterPopup: false, // 修复初始值为 true 的问题
      showCountTypePicker: false, // 控制线索类型选择器的显示
      countTypeOptions: ['线索条数', '需求数量'], // 示例选项
      seriesOptions: ['品系1', '品系2'],
      form: {
        series: null
      }
    }
  },
  methods: {
    // 处理表单提交
    handleFilterSubmit(formData) {
      // console.log('表单提交数据：', formData)
      this.$emit('apply-filters', formData)
      this.showFilterPopup = false
    },
    handleFilterReset(formData) {
      const today = new Date()
      const firstDayOfYear = new Date(today.getFullYear(), 0, 1)

      const resetData = {
        ...formData,
        startDate: dayjs(firstDayOfYear).format('YYYY/MM/DD'),
        endDate: dayjs(today).format('YYYY/MM/DD'),
        series: null
      }

      this.form.series = null
      this.$emit('apply-filters', resetData)
      this.showFilterPopup = false
    },
    handleColse() {
      this.showFilterPopup = false
    },
    onDateConfirm(date) {
      const [start, end] = date
      this.showCalendar = false
      this.$emit('date-update', {
        startDate: dayjs(start).format('YYYY/MM/DD'),
        endDate: dayjs(end).format('YYYY/MM/DD')
      })
    },
    // 新增方法：处理线索类型选择确认
    onSeriesConfirm(value, index) {
      // console.log('线索类型选择确认：', index)
      const newSeries = this.getDictByType('new_clue_strain')[index].valueCode
      this.form.series = value
      this.showCountTypePicker = false
      this.$emit('update:dateRange', {
        ...this.dateRange,
        series: newSeries
      })
    },
    handleTabClick(name, title) {
      console.log('tab clicked:', title)
      // 只传递countType变化给父组件
      this.$emit('update:dateRange', { countType: title })
    }
  }
}
</script>

<style scoped>
/* 保留必要的样式 */
.date-selector {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin: 15px 0;
  background-image: linear-gradient(
    180deg,
    rgba(228, 240, 251, 0.72) 3%,
    rgba(255, 255, 255, 0.8) 18%
  );
  border: 1px solid rgba(255, 255, 255, 1);
  border: 1px solid white;
  border-radius: 10px;
  padding: 8px;
}

.tab-nav {
  display: flex;
  gap: 40px;
  margin: 0 16px;
  border-radius: 8px;
}

.tab-item {
  text-align: center;
  padding: 12px 0;
  cursor: pointer;
}

.tab-item.active {
  font-weight: bold;
  position: relative;
  font-size: 18px;
  color: #1b7aff;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #1b7aff;
}

.date-input {
  flex: 1;
  color: #585858;
  padding: 8px 12px;
  border: 1px solid #dcdfe6;
  background-color: #ffffff;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.to {
  margin: 0 10px;
  font-size: 14px;
  color: #000000;
  text-align: center;
  line-height: 22px;
  font-weight: 400;
}

.arrow {
  margin-left: 8px;
  font-size: 12px;
}
.filter-bar {
  display: flex;
  color: #353535;
  /* margin-left: 10px; */
  flex: 1;
  width: 100%;
  border-radius: 4px;
  overflow: hidden;
  cursor: pointer;
  gap: 8px;
}

/* 弹出层样式补充 */
.filter-popup {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.popup-header {
  font-size: 16px;
  font-weight: bold;
  padding: 15px;
  border-bottom: 1px solid #eee;
}

.form-actions {
  display: flex;
  justify-content: space-around;
  padding: 15px;
  background-color: white;
  border-top: 1px solid #eee;
  position: fixed;
  bottom: 0;
  left: 20%;
  right: 0;
  z-index: 100;
}

/* 修复CSS语法错误 */
.filter-popup {
  padding: 15px;
  height: 100%;
  box-sizing: border-box;
}

/* 弹出层样式 */
.filter-popup {
  padding: 15px;
  height: 100%;
  box-sizing: border-box;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #eee;
}

.popup-actions {
  display: flex;
  justify-content: space-around;
  margin-top: 20px;
  padding: 15px;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  border-top: 1px solid #eee;
}

.filter-item {
  display: flex;
  flex: 1;
  background-color: #ffffff;
  align-items: center;
  padding: 8px 12px;
  cursor: pointer;
  color: #353535;
  justify-content: center;
}

.filter-actions {
  display: flex;
  gap: 8px;
}

.filter-btn,
.search-btn {
  background-color: #ffffff;
  /* margin-left: 5px; */
  padding: 8px 16px;
  color: #1b7aff;
  border-radius: 4px;
}
</style>
