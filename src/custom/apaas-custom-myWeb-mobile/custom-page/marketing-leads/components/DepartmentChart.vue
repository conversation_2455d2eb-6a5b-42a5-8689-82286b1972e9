<template>
  <div>
    <div class="department-chart">
      <div class="chart-container">
        <div class="chart-header">
          <div style="display: flex;justify-content: center; gap: 6px;align-items: center;">
            <img src="@/assets/icon/lead/zs.png" style="width: 24px; height: 24px;" />
            <h3>各市场部进度</h3>
          </div>
          <span style="position: absolute;bottom: -18px; left: 10;color: #838383;">市场部</span>
        </div>
        <div class="charts-box">
          <qiun-vue-ucharts
            type="bar"
            :opts="opts"
            :chartData="chartData"
            :canvasId="canvasId"
            background="rgba(0,0,0,1)"
            :dataLabel="false"
          />
        </div>
      </div>

      <!-- Data Table -->
      <div class="data-table-container">
        <div class="table-header">
          <div class="header-cell">
            市场部
          </div>
          <div class="header-cell">
            总数
          </div>
          <div class="header-cell">
            线索录入
          </div>
          <div class="header-cell">
            洽谈中
          </div>
          <div class="header-cell">
            签单
          </div>
          <div class="header-cell">
            丢单
          </div>
        </div>
        <div class="table-body">
          <div
            v-for="(item, index) in tableData"
            :key="item.region"
            class="table-row"
            :class="{ even: index % 2 === 1 }"
          >
            <div class="table-cell" @click="handlerClick(item, index, 0, '市场部')">
              {{ item.region }}
            </div>
            <div class="table-cell total" @click="handlerClick(item, index, 1, '总数')">
              {{
                (item.progress0 || 0) +
                  (item.progress1 || 0) +
                  (item.progress2 || 0) +
                  (item.progress3 || 0)
              }}
            </div>
            <div class="table-cell" @click="handlerClick(item, index, 2, '线索录入')">
              {{ item.progress0 || '-' }}
            </div>
            <div class="table-cell negotiating" @click="handlerClick(item, index, 3, '洽谈中')">
              {{ item.progress1 || '-' }}
            </div>
            <div class="table-cell signed" @click="handlerClick(item, index, 4, '签单')">
              {{ item.progress2 || '-' }}
            </div>
            <div class="table-cell lost" @click="handlerClick(item, index, 5, '丢单')">
              {{ item.progress3 || '-' }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import qiunVueUcharts from '@qiun/vue-ucharts'
import dayjs from 'dayjs'
import _ from 'lodash'

export default {
  name: 'DepartmentChart',
  components: {
    qiunVueUcharts
  },
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      canvasId: '',
      chartData: {},
      debouncedFetchData: null,
      opts: {
        dataLabel: false,
        color: ['#FF8E16', '#07CFC1', '#1677FF', '#BAE0FF'],
        padding: [0, 10, 0, 20],
        enableScroll: false,
        legend: {
          show: true,
          position: 'top',
          float: 'right',
          fontSize: 10,
          padding: 2,
          margin: 4,
          fontColor: '#838383'
        },
        xAxis: {
          boundaryGap: 'justify',
          gridColor: '#838383',
          gridType: 'dash',
          calibration: true,
          disableGrid: false
        },
        yAxis: {},
        extra: {
          bar: {
            type: 'stack',
            width: 20,
            activeBgColor: '#ffffff',
            activeBgOpacity: 0.18,
            categoryGap: 10,
            seriesGap: 8
          },
          tooltip: {
            bgColor: '#FFFFFF',
            fontColor: '#333333',
            bgOpacity: 1
          },
          markLine: {
            type: 'dash'
          }
        }
      },
      tableData: []
    }
  },
  computed: {
    averageRate() {
      const rateData = this.chartData.series.find((item) => item.name === '达成率')?.data || []
      if (rateData.length === 0) return 0
      const total = rateData.reduce((sum, item) => sum + item, 0)
      return Math.round(total / rateData.length)
    },
    maxRate() {
      const rateData = this.chartData.series.find((item) => item.name === '达成率')?.data || []
      if (rateData.length === 0) return 0
      return Math.max(...rateData)
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.initData()
        this.debouncedFetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.initData()
        this.debouncedFetchData()
      },
      deep: true
    }
  },
  created() {
    this.canvasId = this.generateRandomString(32)
    this.initData()
    this.debouncedFetchData = _.debounce(this.fetchData, 300)
  },
  mounted() {
    this.debouncedFetchData() // 首次加载也通过防抖函数调用
  },
  destroyed() {
    if (this.debouncedFetchData) {
      this.debouncedFetchData.cancel()
    }
  },
  methods: {
    generateRandomString(length) {
      const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
      let result = ''
      for (let i = 0; i < length; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length))
      }
      return result
    },
    initData() {
      this.chartData = {}
      this.tableData = []
    },
    async fetchData() {
      try {
        const response = await this.fetchChartData()
        console.log('response', response)
        this.canvasId = this.generateRandomString(32)
        this.tableData = response
        let res = {
          categories: response.map((item) => item.region),
          series: [
            {
              name: '线索录入',
              data: response.map((item) => item?.progress0 || 0),
              textColor: '#FFFFFF'
            },
            { name: '洽谈中', data: response.map((item) => item?.progress1 || 0) },
            { name: '签单', data: response.map((item) => item?.progress2 || 0) },
            { name: '丢单', data: response.map((item) => item?.progress3 || 0) }
          ]
        }

        this.chartData = JSON.parse(JSON.stringify(res))
        console.log('测试', this.chartData)
      } catch (error) {
        console.error('Failed to fetch department chart data:', error)
      } finally {
        console.log('结束')
      }
    },
    async fetchChartData() {
      return new Promise((resolve, reject) => {
        const params = { ...this.dateRange }
        if (params.startDate) {
          params.startDate = dayjs(params.startDate).format('YYYY-MM-DD 00:00:00')
        }
        if (params.endDate) {
          params.endDate = dayjs(params.endDate).format('YYYY-MM-DD 23:59:59')
        }
        this.$request({
          url: '/custom/clue/queryClueByMarket',
          method: 'post',
          params: params,
          disableSuccessMsg: true,
          headers: {
            'Content-Type': 'application/json'
          }
        })
          .then((response) => {
            resolve(this.groupByRegionAndProgress(response.data))
          })
          .catch((error) => {
            reject(error)
          })
      })
    },
    groupByRegionAndProgress(data) {
      console.log('数据：', data)
      const map = {}

      for (const item of data) {
        const { ids, region, sum, progress } = item

        if (!map[region]) {
          map[region] = {
            ids,
            region: region
          }
        }

        if (!map[region][progress]) {
          map[region][progress] = 0
        }
        map[region][progress] += sum
      }

      return Object.values(map)
    },
    handlerClick(item, index, columnIndex, columnName) {
      // console.log('点击的行数据：', item, index)
      // console.log('点击的列：', columnIndex, columnName)
      this.$emit('clickTableRow', { ...item, columnName })
    },

    // 备用方案：通过事件对象获取点击的列
    handlerRowClick(event, item, index) {
      const row = event.currentTarget
      const cells = row.querySelectorAll('.table-cell')
      const clickedElement = event.target.closest('.table-cell')

      if (clickedElement) {
        const columnIndex = Array.from(cells).indexOf(clickedElement)
        const columnNames = ['市场部', '总数', '线索录入', '洽谈中', '签单', '丢单']
        const columnName = columnNames[columnIndex]

        this.handlerClick(item, index, columnIndex, columnName)
      }
    }
  }
}
</script>

<style scoped>
.department-chart {
  padding-bottom: 20px;
  overflow-y: auto;
  height: 60vh;
  scrollbar-width: thin;
  scrollbar-color: #cecece #f1f1f1;
  border: 1px solid rgba(255, 255, 255, 1);
  background-image: linear-gradient(
    180deg,
    rgba(245, 246, 247, 0.9) 0%,
    rgba(255, 255, 255, 0.9) 100%
  );
}

.chart-header {
  display: flex;
  padding: 10px 10px;
  position: relative;
}

.chart-header h3 {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}

.chart-legend {
  display: flex;
  gap: 12px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.legend-color {
  width: 8px;
  height: 8px;
  border-radius: 2px;
}

.legend-color.blue {
  background: #1890ff;
}

.legend-color.yellow {
  background: #ffd700;
}

.legend-color.orange {
  background: #ff7f00;
}

.legend-color.gray {
  background: #d9d9d9;
}

.chart-container {
  width: 100%;
  border-radius: 12px;
}

.charts-box {
  width: 100%;
  height: 300px;
  padding-bottom: 40px;
}

.data-table-container {
  background: white;
  border-radius: 12px;
  margin: 16px 0;
  overflow: hidden;
  padding: 0 4px;
}

.table-header {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  background: rgba(0, 0, 0, 0.04);
  font-weight: 600;
  font-size: 14px;
  color: #333;
}

.header-cell {
  padding: 16px 8px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
}

.header-cell:last-child {
  border-right: none;
}

.table-body {
  max-height: 50vh;
  overflow-y: auto;
}

.table-row {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  transition: background-color 0.2s;
}

.table-row:hover {
  background-color: #f8f9fa;
}

.table-row.even {
  background-color: #fafafa;
}

.table-row.even:hover {
  background-color: #f0f0f0;
}

.table-cell {
  padding: 12px 8px;
  text-align: center;
  font-size: 13px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-cell:last-child {
  border-right: none;
}

.table-cell.signed {
  color: #1890ff;
  font-weight: 600;
}

.table-cell.lost {
  color: #ff4d4f;
  font-weight: 600;
}

.table-body::-webkit-scrollbar {
  width: 4px;
}

.table-body::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 2px;
}

.table-body::-webkit-scrollbar-thumb {
  background: #cecece;
  border-radius: 2px;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: #bbb;
}
</style>
