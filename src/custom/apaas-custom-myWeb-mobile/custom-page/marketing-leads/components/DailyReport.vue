<template>
  <div class="lead-progress">
    <!-- Stats Cards -->
    <div class="stats-cards">
      <div class="stat-card light-blue">
        <div class="stat-icon">
          日
        </div>
        <div class="stat-content">
          <div class="stat-label">
            当日
          </div>
          <div class="stat-value">
            {{ statsData.today }}
          </div>
        </div>
      </div>
      <div class="stat-card purple">
        <div class="stat-icon">
          月
        </div>
        <div class="stat-content">
          <div class="stat-label">
            本月累计
          </div>
          <div class="stat-value">
            {{ statsData.toMoon }}
          </div>
        </div>
      </div>
      <div class="stat-card orange">
        <div class="stat-icon">
          年
        </div>
        <div class="stat-content">
          <div class="stat-label">
            年累计
          </div>
          <div class="stat-value">
            {{ statsData.toYear }}
          </div>
        </div>
      </div>
      <div class="stat-card blue">
        <div class="stat-icon">
          成
        </div>
        <div class="stat-content">
          <div class="stat-label">
            达成量
          </div>
          <div class="stat-value">
            {{ statsData.achieve }}
          </div>
        </div>
      </div>
    </div>
    <!-- Data Table -->
    <div class="data-table">
      <div v-for="(department, index) in departmentData" :key="index" class="department-section">
        <div class="department-header" @click="toggleDepartment(index)">
          <div class="report-header">
            <h3 class="department-name">
              {{ formatDepartmentName(department.region) }}
            </h3>
          </div>
          <div class="cell header-cell">
            <div class="department-label">
              当日
            </div>
            <div>{{ department.today }}</div>
          </div>
          <div class="cell header-cell">
            <div class="department-label">
              本月累计
            </div>
            <div>{{ department.toMoon }}</div>
          </div>
          <div class="cell header-cell">
            <div class="department-label">
              年累计
            </div>
            <div>{{ department.toYear }}</div>
          </div>
          <div class="cell header-cell">
            <div class="department-label">
              达成量
            </div>
            <div class="text-blue">
              {{ department.achieve }}
            </div>
          </div>
          <span class="expand-icon" :class="{ expanded: department.expanded }">▼</span>
        </div>
        <div v-show="department.brandList.length > 0 && department.expanded">
          <div v-for="brand in department.brandList" :key="brand.brand" class="data-row">
            <div class="cell brand">
              {{ brand.brand }}
            </div>
            <div class="cell">
              {{ brand.today }}
            </div>
            <div class="cell">
              {{ brand.toMoon }}
            </div>
            <div class="cell">
              {{ brand.toYear }}
            </div>
            <div class="cell">
              <span class="text-blue">
                {{ brand.achieve }}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- Pull to Refresh -->
    <div v-if="refreshing" class="refresh-indicator">
      <div class="loading-spinner small"></div>
      <span>刷新中...</span>
    </div>
  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  name: 'LeadProgress',
  props: {
    dateRange: {
      type: Object,
      required: true
    },
    filters: {
      type: Object,
      required: true
    }
  },
  data() {
    return {
      statsData: {
        today: 0,
        toMoon: 0,
        achieve: 0,
        toYear: 0
      },
      departmentData: [],
      refreshing: false
    }
  },
  computed: {
    totalStats() {
      return this.departmentData.reduce(
        (acc, department) => {
          department.data.forEach((row) => {
            acc.today += Number(row.today) || 0
            acc.toMoon += Number(row.toMoon) || 0
            acc.achieve += Number(row.achieve) || 0
          })
          console.log('内容', acc)
          return acc
        },
        { today: 0, monthly: 0, achieved: 0 }
      )
    }
  },
  watch: {
    dateRange: {
      handler() {
        this.fetchData()
      },
      deep: true
    },
    filters: {
      handler() {
        this.fetchData()
      },
      deep: true
    }
  },
  created() {
    this.initData()
  },
  mounted() {
    this.fetchData()
  },
  methods: {
    async initData() {
      // 初始化默认数据
      this.departmentData = []
    },
    formatDepartmentName(name) {
      if (!name || name.length <= 2) return name
      // 仅在第一行第二个字符后换行
      return name.substring(0, 2) + '\n' + name.substring(2, name.length)
    },
    async fetchData() {
      try {
        this.$emit('loading', true)

        // 模拟API调用
        const statsResponse = await this.fetchDepartmentData()

        // 使用循环累加函数计算统计数据
        this.statsData = statsResponse.reduce(
          (acc, department) => {
            acc.today += Number(department.today) || 0
            acc.toMoon += Number(department.toMoon) || 0
            acc.achieve += Number(department.achieve) || 0
            acc.toYear += Number(department.toYear) || 0
            return acc
          },
          {
            today: 0,
            toMoon: 0,
            achieve: 0,
            toYear: 0
          }
        )
        // 初始化每个部门的展开状态
        this.departmentData = statsResponse.map((dept) => ({
          ...dept,
          expanded: false
        }))

        this.$emit('data-updated', {
          stats: this.statsData,
          departments: this.departmentData
        })
      } catch (error) {
        console.error('Failed to fetch lead progress data:', error)
        this.$emit('error', '获取线索进度数据失败，请重试')
      } finally {
        this.$emit('loading', false)
      }
    },

    async fetchStatsData() {
      // 模拟API调用
      return new Promise((resolve) => {
        setTimeout(() => {
          resolve({
            today: 42,
            toMoon: 156,
            achieve: 89,
            toYear: 1200
          })
        }, 1000)
      })
    },

    async fetchDepartmentData() {
      // 模拟API调用
      return new Promise(async (resolve, reject) => {
        const params = { ...this.dateRange }
        if (params.startDate) {
          params.startDate = dayjs(params.startDate).format('YYYY-MM-DD 00:00:00')
        }
        if (params.endDate) {
          params.endDate = dayjs(params.endDate).format('YYYY-MM-DD 23:59:59')
        }
        this.$request({
          url: '/custom/clue/queryClueByReport',
          method: 'post',
          params: params,
          disableSuccessMsg: true,
          headers: {
            'Content-Type': 'application/json'
          }
        })
          .then((response) => {
            resolve(response.data)
          })
          .catch((error) => {
            // console.error('Failed to fetch department data:', error)
            reject(error)
          })
      })
    },
    jsonToString(json) {
      return JSON.parse(json)
    },
    updateDepartmentData(apiData) {
      apiData.forEach((item) => {
        const department = this.departmentData.find((d) => d.region === item.region)
        if (department) {
          department.brandList = item.brandList
        }
      })
    },

    toggleDepartment(index) {
      if (this.departmentData[index]) {
        this.departmentData[index].expanded = !this.departmentData[index].expanded
      }
    },

    async refreshData() {
      this.refreshing = true
      try {
        await this.fetchData()
      } finally {
        this.refreshing = false
      }
    }
  }
}
</script>

<style scoped>
.report-header {
  min-width: 66px;
  max-width: 70px;
  padding: 10px 10px;
  background-image: linear-gradient(
    90deg,
    rgba(214, 231, 249, 0.9) 27%,
    rgba(240, 241, 251, 1) 100%
  );
  border-radius: 0px 8px 8px 0px;
  line-height: 20px;
  font-size: 16px;
  position: relative;
}

.report-header::after {
  content: '';
  position: absolute;
  top: 4px;
  right: 3px;
  width: 25px;
  height: 25px;
  background-image: url('../assets/img/city.png');
  background-size: cover;
  background-repeat: no-repeat;
}

.department-name {
  font-weight: 800;
  word-break: break-word;
  white-space: pre-wrap;
}
.lead-progress {
  padding-bottom: 20px;
  overflow-y: auto;
  height: 60vh;
  scrollbar-width: thin;
  scrollbar-color: #cecece #f1f1f1;
}

.stats-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(80px, 1fr));
  /* gap: 8px; */
  margin-bottom: 16px;
  padding: 4px 0px;
  background-color: #fff;
  border-radius: 8px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 12px 8px;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.stat-content {
  display: flex;
  flex-direction: column;
}
.stat-icon {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 4px;
  font-size: 12px;
  font-weight: bold;
  color: white;
}

.stat-card.blue .stat-icon {
  background: #1890ff;
}
.stat-card.purple .stat-icon {
  background: #722ed1;
}
.stat-card.orange .stat-icon {
  background: #fa8c16;
}
.stat-card.light-blue .stat-icon {
  background: #13c2c2;
}
.text-blue {
  /* background: #1890ff; */
  color: #1890ff;
}
.stat-label {
  font-size: 10px;
  color: #666;
  margin-bottom: 4px;
}

.stat-value {
  font-size: 16px;
  font-weight: bold;
  color: #333;
}

.data-table {
  border-radius: 8px;
  overflow: hidden;
  /* box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1); */
}

.department-section {
  background: white;
  border-bottom: 1px solid #f0f0f0;
  border-radius: 8px;
  margin: 10px 0px;
}

.department-section:last-child {
  border-bottom: none;
}

.department-header {
  display: grid;
  grid-template-columns: min-content repeat(4, 1fr) auto;
  align-items: center;
  padding: 6px 16px 6px 0px;
  background: #fafafa;
  font-size: 14px;
  font-weight: 500;
  color: #333;
  cursor: pointer;
  transition: background-color 0.2s;
}

.expand-icon {
  color: #999;
  font-size: 12px;
  transition: transform 0.2s;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

.data-row {
  display: grid;
  grid-template-columns: min-content repeat(4, 1fr);
  padding: 8px 16px;
  border-bottom: 1px solid #f5f5f5;
  font-size: 12px;
  padding-right: 24px;
}

.data-row:last-child {
  border-bottom: none;
}

.cell.header-cell {
  background: #f8f9fa;
  font-weight: 500;
  color: #666;
  font-size: 13px;
}

.cell {
  padding: 4px;
  text-align: center;
  line-height: 20px;
}

.cell.brand {
  text-align: left;
  color: #333;
  min-width: 60px;
}

.cell.rate {
  color: #1890ff;
}

.refresh-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px;
  color: #666;
  font-size: 14px;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid #4a90e2;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}
.department-label{
  font-size: 10px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}
</style>
