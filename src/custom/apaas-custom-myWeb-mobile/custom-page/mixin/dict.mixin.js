// src/mixins/dict.mixin.js
import Vue from 'vue'

export default {
  data() {
    return {
      dictData: {},
      loading: false,
      error: null
    }
  },
  created() {
    this.fetchDictData()
  },
  methods: {
    async fetchDictData() {
      this.loading = true
      try {
        const response = await Vue.prototype.$request({
          url: '/custom/crm-common/dataDictionary/queryDataDictionaryValue',
          method: 'post',
          disableSuccessMsg: true,
          params: ['new_clue_strain', 'CRM_XSLY', 'strain', 'client_xs_type', 'new_clue_strain']
        })

        // 将扁平数组按 dictionaryCode 分组
        const groupedData = response.data.reduce((acc, item) => {
          const { dictionaryCode } = item
          if (!acc[dictionaryCode]) {
            acc[dictionaryCode] = []
          }
          // 转换为组件常用的 { value, label } 格式
          acc[dictionaryCode].push({
            value: item.valueCode,
            label: item.valueName,
            ...item // 保留原始字段
          })
          return acc
        }, {})

        this.dictData = groupedData
        this.error = null
      } catch (error) {
        console.error('获取字典数据失败:', error)
        this.error = error.message || '获取数据字典失败'
      } finally {
        this.loading = false
      }
    },
    getDictByType(type) {
      return this.dictData[type] || []
    },
    getDictLabel(type, value) {
      const dict = this.dictData[type] || []
      const item = dict.find((item) => item.value === value)
      return item ? item.label : value
    }
  }
}
