<template>
  <div>
    <!-- <van-field clickable>
      <template #input>
        <input
          v-model="formValue"
          type="text"
          placeholder="请选择品系"
          :style="false ? 'color:#303133;width: 100%' : 'color:#969799;width: 100%'"
        />
      </template>
    </van-field>
    <van-popup v-model="cityShowP" position="bottom">
      <van-search
        v-model="cityValue"
        placeholder="请输入搜索关键词"
        input-align="center"
        @input="remoteMethod"
      ></van-search>
      <van-picker show-toolbar :loading="loading" :columns="options" @confirm="choose">
        <template #option="option">
          <div style="display: flex; flex-direction: column; align-items: center;">
            <div>{{ option.value }}</div>
          </div>
        </template>
      </van-picker>
    </van-popup> -->
    <qiun-data-charts type="bar" :opts="opts" :chartData="chartData" />
  </div>
</template>

<script>
import qiunVueUcharts from '@qiun/vue-ucharts'

export default {
  name: 'ApaasCustomMyWebmobile',
  components: {
    'qiun-data-charts': qiunVueUcharts
  },
  data: function() {
    return {
      chartData: {},
      formValue: '',
      cityShowP: false,
      cityValue: '',
      options: [],
      plate: '卡车',
      loading: false,
      opts: {
        color: [
          '#1890FF',
          '#91CB74',
          '#FAC858',
          '#EE6666',
          '#73C0DE',
          '#3CA272',
          '#FC8452',
          '#9A60B4',
          '#ea7ccc'
        ],
        padding: [15, 30, 0, 5],
        enableScroll: false,
        legend: {},
        xAxis: {
          boundaryGap: 'justify',
          disableGrid: false,
          min: 0,
          axisLine: false,
          max: 70
        },
        yAxis: {},
        extra: {
          bar: {
            type: 'stack',
            width: 30,
            meterBorde: 1,
            meterFillColor: '#FFFFFF',
            activeBgColor: '#000000',
            activeBgOpacity: 0.08,
            categoryGap: 2
          }
        }
      }
    }
  },
  computed: {},
  created() {
    this.getServerData()
  },
  methods: {
    test() {},
    choose(item) {
      let uuid = this.quesFormNameWidget('strain')
      for (var i in this.formData) {
        if (uuid === i) {
          this.formData[i] = item.value
        }
      }
    },
    getPlate() {
      let uuid = this.quesFormNameWidget('plate')
      console.log(uuid)
      if (this.formData[uuid] !== undefined) {
        console.log(this.formData[uuid])
        for (var i in this.formData) {
          console.log(this.formData, uuid, this.plateOptions)
          if (uuid === i) {
            this.plateOptions.forEach((v) => {
              if (this.formData[i][0] === v.key) {
                this.plate = v.value // 板块
              }
            })
          }
        }
      }
    },
    getSelectDataID(keyword, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dataDictionaryList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { keyword: keyword, appId: '306383473654890496' }
      }).asyncThen((resp) => {
        if (resp.code === 'ok') {
          if (resp.table.length !== 0) {
            this.getSelectData(resp.table[0].id, table)
          }
        }
      })
    },
    getSelectData(id, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dictionaryValueList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { page: 1, pageSize: 23, keyword: '', dictionaryId: id }
      }).asyncThen((resp) => {
        resp.table.forEach((v) => {
          let pool = { value: '', key: '' }
          pool.value = v.valueName
          pool.key = v.valueCode
          table.push(pool)
        })
      })
    },
    // 查询组件uuid
    quesFormNameWidget(value) {
      const widget = this.allTileFormItemList.find((item) => {
        if (item.modelField && item.modelField.split('.')[1] === value) {
          return true
        }
      })
      if (widget) {
        return widget.uuid
      }
      return null
    },
    // 公司简称查询
    remoteMethod() {
      // this.getPlate()
      if (this.plate !== '') {
        this.loading = true
        this.options = []
        this.$request({
          url:
            'https://apaas.app.yuchai.com/mpaas-api/gateway/ycloud-decision-making-service/wlxz/query',
          method: 'post',
          disableSuccessMsg: true, // 暂时必须，忽略消息影响
          params: {
            type: '1',
            zywbk: this.plate,
            strain: this.cityValue,
            zcpzxl: ''
          }
        }).asyncThen((resp) => {
          this.loading = false
          let data = resp.table
          data.forEach((element) => {
            let pool = { label: '', value: '' }
            pool.label = element
            pool.value = element
            this.options.push(pool)
          })
        })
      } else {
        this.$message({
          message: '请先选择板块！！！',
          type: 'warning'
        })
      }
    },
    getServerData() {
      // 模拟从服务器获取数据时的延时
      setTimeout(() => {
        // 模拟服务器返回数据，如果数据格式和标准格式不同，需自行按下面的格式拼接
        let res = {
          categories: ['未知', '政企数字化事业部', '北京', '临时组织'],
          series: [
            {
              name: '抢单',
              data: [0, 0, 12, 0],
              textColor: '#FFFFFF'
            },
            {
              name: '签单',
              data: [30, 10, 23, 790]
            },
            {
              name: '丢单',
              data: [0, 0, 0, 0]
            }
          ]
        }
        this.chartData = JSON.parse(JSON.stringify(res))
      }, 500)
    }
  }
}
</script>

<style lang="scss">
// .myWeb-mobile {
//   .van-field__control {
//     display: block;
//     box-sizing: border-box;
//     width: 100%;
//     min-width: 0;
//     margin: 0;
//     padding: 0;
//     //  color: #0f0fce !important;
//     line-height: inherit;
//     text-align: left;
//     background-color: transparent;
//     border: 0;
//     resize: none;
//   }
//   .van-field__control2 {
//     display: block;
//     box-sizing: border-box;
//     width: 100%;
//     min-width: 0;
//     margin: 0;
//     padding: 0;
//     color: #010105;
//     line-height: inherit;
//     text-align: left;
//     background-color: transparent;
//     border: 0;
//     resize: none;
//   }
// }
.charts-box {
  width: 100%;
  height: 300px;
}
</style>
