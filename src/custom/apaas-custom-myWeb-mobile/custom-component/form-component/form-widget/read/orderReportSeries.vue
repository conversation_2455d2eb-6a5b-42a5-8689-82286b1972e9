<template>
  <div class="form-widget form-custom-select">
    <x-proxy-form-item
      :isInTable="widget.isInTable"
      :label="widget.label"
      :validatorRules="validatorRules"
      :validateKey="validateKey"
      :validateInfo="validateInfo"
      :disabled="widget.readOnly"
    >
      {{ formValue }}
    </x-proxy-form-item>
  </div>
</template>

<script>
import FormWidgetConfigMixin from '@/mixin/form-widget.mixin'
export default {
  name: 'ReadOrderReportSeries',
  mixins: [FormWidgetConfigMixin],
  data() {
    return {}
  },
  created() {},
  methods: {}
}
</script>
