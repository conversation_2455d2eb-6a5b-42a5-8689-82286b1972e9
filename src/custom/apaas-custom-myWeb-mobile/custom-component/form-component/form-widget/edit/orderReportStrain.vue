<template>
  <div class="form-widget form-custom-select">
    <div v-show="inCard">
      {{ formValue }}
    </div>
    <x-proxy-form-item
      v-show="!inCard"
      :isInTable="widget.isInTable"
      :showRequired="showRequired"
      :label="widget.label"
      :validatorRules="validatorRules"
      :validateKey="validateKey"
      :validateInfo="validateInfo"
      :disabled="widget.readOnly"
    >
      <van-field :clickable="!widget.readOnly">
        <template #input>
          <input
            v-model="formValue"
            type="text"
            placeholder="请选择品系"
            :style="widget.readOnly ? 'color:#969799;width: 100%' : 'color:#303133;width: 100%'"
            readonly="true"
            @click="openChoose"
          />
        </template>
      </van-field>
      <van-popup v-model="show" position="bottom">
        <van-search
          v-model="searchValue"
          placeholder="请输入搜索关键词"
          input-align="center"
          @input="remoteMethod"
        ></van-search>
        <van-picker
          show-toolbar
          :loading="loading"
          :columns="options"
          @confirm="choose"
          @cancel="show = false"
        >
        </van-picker>
      </van-popup>
    </x-proxy-form-item>
  </div>
</template>

<script>
import FormWidgetConfigMixin from '@/mixin/form-widget.mixin'
export default {
  name: 'EditOrderReportStrain',
  mixins: [FormWidgetConfigMixin],
  data() {
    return {
      options: [],
      // value: '',
      plate: '',
      loading: false,
      plateOptions: [],
      allTileFormItemList: [],
      show: false,
      searchValue: ''
    }
  },
  computed: {
    inCard() {
      const path = this.$route.path
      if (path.includes('son-table-item-detail')) {
        return false
      }
      return true
    }
  },
  created() {
    this.allTileFormItemList = this.formEngine.formDataControl.allTileFormItemList || []
    //  this.getSelectData('385441718654205952', this.plateOptions) // 板块
    this.getSelectDataID('所属板块', this.plateOptions) // 板块
  },
  methods: {
    choose(item) {
      let uuid = this.quesFormNameWidget('strain')
      for (var i in this.formData) {
        if (uuid === i) {
          this.formData[i] = item
        }
      }
      this.show = false
    },
    getPlate() {
      let uuid = this.quesFormNameWidget('plate')
      if (this.formData[uuid] !== undefined) {
        for (var i in this.formData) {
          if (uuid === i) {
            this.plateOptions.forEach((v) => {
              if (this.formData[i][0] === v.key) {
                this.plate = v.value // 板块
              }
            })
          }
        }
      }
    },
    getSelectDataID(keyword, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dataDictionaryList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { keyword: keyword, appId: '306383473654890496' }
      }).asyncThen((resp) => {
        if (resp.code === 'ok') {
          if (resp.table.length !== 0) {
            this.getSelectData(resp.table[0].id, table)
          }
        }
      })
    },
    getSelectData(id, table) {
      this.$request({
        url:
          '/xdap-app/dataDictionary/query/dictionaryValueList?SECURITY_INFO=eyJhcHBJZCI6IjMwNjM4MzQ3MzY1NDg5MDQ5NiJ9',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: { page: 1, pageSize: 23, keyword: '', dictionaryId: id }
      }).asyncThen((resp) => {
        resp.table.forEach((v) => {
          let pool = { value: '', key: '' }
          pool.value = v.valueName
          pool.key = v.valueCode
          table.push(pool)
        })
      })
    },
    // 查询组件uuid
    quesFormNameWidget(value) {
      const widget = this.allTileFormItemList.find((item) => {
        if (item.modelField && item.modelField.split('.')[1] === value) {
          return true
        }
      })
      if (widget) {
        return widget.uuid
      }
      return null
    },
    openChoose() {
      if (!this.widget.readOnly) {
        this.getPlate()
        if (this.plate !== '') {
          this.show = true
          this.remoteMethod()
        } else {
          this.$message({
            message: '请先选择板块！！！',
            type: 'warning'
          })
        }
      }
    },
    // 公司简称查询
    remoteMethod() {
      this.loading = true
      this.options = []
      this.$request({
        url:
          'https://apaas.app.yuchai.com/mpaas-api/gateway/ycloud-decision-making-service/wlxz/query',
        method: 'post',
        disableSuccessMsg: true, // 暂时必须，忽略消息影响
        params: {
          type: '1',
          zywbk: this.plate,
          strain: this.searchValue,
          zcpzxl: ''
        }
      }).asyncThen((resp) => {
        if (resp.code === 'ok') {
          this.loading = false
          this.options = [...resp.table]
        }
      })
    }
  }
}
</script>

<style lang="scss"></style>
