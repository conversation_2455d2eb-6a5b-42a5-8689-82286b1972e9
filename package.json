{"name": "xdap-h5", "version": "2.14.6", "private": true, "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build --mode dev", "lint": "vue-cli-service lint", "cm": "git-cz", "build:zip": "x-apaas-cli build apaas-custom-myWeb-mobile"}, "dependencies": {"@babel/eslint-parser": "^7.13.14", "@babel/polyfill": "^7.8.7", "@babel/runtime": "^7.13.10", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^8.3.6", "@qiun/vue-ucharts": "^2.5.0-20230101", "@x-apaas/x-apaas-frontend-i18n": "^3.3.4-rc.1", "@x-apaas/x-dcloud-business-event": "^3.3.4-rc.1", "@x-apaas/x-dcloud-low-code-engine": "^3.3.4-rc.1", "@x-apaas/x-dcloud-page-engine": "^3.3.4-rc.1", "@x-apaas/x-dcloud-page-mobile": "^3.3.4-rc.1", "@x-apaas/x-lib-rule-engine": "^3.3.4-rc.1", "axios": "^0.19.2", "babel-plugin-component": "^1.1.1", "compressorjs": "^1.0.6", "core-js": "^3.6.5", "cube-ui": "~1.12.15", "dayjs": "^1.8.30", "lodash-es": "^4.17.21", "normalize.css": "^8.0.1", "ts-lib-tools": "^0.12.1", "vant": "^2.12.54", "vconsole": "^3.3.4", "vue": "^2.6.12", "vue-amap": "^0.5.10", "vue-cookies": "^1.7.3", "vue-hash-calendar": "^1.2.12", "vue-i18n": "^8.18.2", "vue-ls": "^3.2.1", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuex": "^3.4.0", "vuex-persist": "^2.2.0"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.4.0", "@vue/cli-plugin-eslint": "~4.4.0", "@vue/cli-plugin-router": "~4.4.0", "@vue/cli-plugin-vuex": "~4.4.0", "@vue/cli-service": "~4.4.0", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-standard": "^4.0.0", "babel-eslint": "^10.1.0", "commitizen": "^4.1.2", "commitlint-config-cz": "^0.13.1", "conventional-changelog-cli": "^2.0.34", "cz-conventional-changelog": "^3.2.0", "cz-customizable": "^6.2.0", "eslint": "^6.7.2", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-vue": "^6.2.2", "husky": "^4.2.5", "lint-staged": "^9.5.0", "node-sass": "^4.12.0", "postcss-px2rem": "^0.3.0", "prettier": "^1.19.1", "sass-loader": "^8.0.2", "stylus": "^0.54.5", "stylus-loader": "^3.0.2", "svg-sprite-loader": "^5.0.0", "vue-cli-plugin-cube-ui": "~0.2.5", "vue-template-compiler": "^2.6.11", "webpack-theme-color-replacer": "^1.3.14"}, "transformModules": {"cube-ui": {"transform": "cube-ui/src/modules/${member}", "kebabCase": true}}, "husky": {"hooks": {"pre-commit": "lint-staged", "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "lint-staged": {"*.{js,jsx,vue}": ["vue-cli-service lint --fix", "git add"]}}